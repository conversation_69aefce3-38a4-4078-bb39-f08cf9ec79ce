<template>
    <div class="inventory-dashboard">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>库存数据看板</h1>
            <p>实时监控库存周转率和进出库数据</p>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-cards">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-arrow-down text-green-500"></i>
                </div>
                <div class="stats-content">
                    <h3>当日入库</h3>
                    <div class="stats-number">{{ dashboardData.today_in || 0 }}</div>
                    <div class="stats-change" :class="dashboardData.today_in_percent >= 0 ? 'positive' : 'negative'">
                        <i :class="dashboardData.today_in_percent >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                        {{ Math.abs(dashboardData.today_in_percent || 0).toFixed(2) }}%
                    </div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-arrow-up text-blue-500"></i>
                </div>
                <div class="stats-content">
                    <h3>当日出库</h3>
                    <div class="stats-number">{{ dashboardData.today_out || 0 }}</div>
                    <div class="stats-change" :class="dashboardData.today_out_percent >= 0 ? 'positive' : 'negative'">
                        <i :class="dashboardData.today_out_percent >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                        {{ Math.abs(dashboardData.today_out_percent || 0).toFixed(2) }}%
                    </div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-calendar-alt text-orange-500"></i>
                </div>
                <div class="stats-content">
                    <h3>月度入库/出库</h3>
                    <div class="stats-number">{{ dashboardData.month_in || 0 }} / {{ dashboardData.month_out || 0 }}</div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-chart-line text-purple-500"></i>
                </div>
                <div class="stats-content">
                    <h3>年度入库/出库</h3>
                    <div class="stats-number">{{ dashboardData.year_in || 0 }} / {{ dashboardData.year_out || 0 }}</div>
                </div>
            </div>
        </div>

        <!-- 库存周转率筛选区域 -->
        <div class="filter-section">
            <h2>库存周转率</h2>
            <p class="filter-description">请选择姓名或输入SKU进行查询</p>
            
            <div class="filter-row">
                <!-- 姓名选择 -->
                <div class="filter-item">
                    <label>姓名</label>
                    <el-select 
                        v-model="filters.selectedName" 
                        placeholder="请选择姓名"
                        @change="onNameChange"
                        @focus="showAdvancedFilters = true"
                        clearable
                    >
                        <el-option
                            v-for="contact in contactList"
                            :key="contact.contacts_name"
                            :label="contact.contacts_name"
                            :value="contact.contacts_name"
                        />
                    </el-select>
                </div>

                <!-- SKU输入 -->
                <div class="filter-item">
                    <label>SKU简码</label>
                    <el-input
                        v-model="filters.skuSearch"
                        placeholder="请输入SKU简码"
                        @focus="showAdvancedFilters = true"
                        @input="onSkuInput"
                        clearable
                    />
                </div>
            </div>

            <!-- 高级筛选器（只有选择姓名或输入SKU后才显示） -->
            <div v-if="showAdvancedFilters && (filters.selectedName || filters.skuSearch)" class="advanced-filters">
                <div class="filter-row">
                    <!-- 开始时间 -->
                    <div class="filter-item">
                        <label>开始时间</label>
                        <el-date-picker
                            v-model="filters.startDate"
                            type="month"
                            placeholder="选择开始月份"
                            format="yyyy-MM"
                            value-format="yyyy-MM"
                        />
                    </div>

                    <!-- 结束时间 -->
                    <div class="filter-item">
                        <label>结束时间</label>
                        <el-date-picker
                            v-model="filters.endDate"
                            type="month"
                            placeholder="选择结束月份"
                            format="yyyy-MM"
                            value-format="yyyy-MM"
                        />
                    </div>

                    <!-- 虚拟仓选择 -->
                    <div class="filter-item">
                        <label>虚拟仓</label>
                        <el-select 
                            v-model="filters.selectedWarehouse" 
                            placeholder="请选择虚拟仓"
                            clearable
                        >
                            <el-option
                                v-for="warehouse in warehouseList"
                                :key="warehouse.fictitious_id"
                                :label="warehouse.fictitious_name"
                                :value="warehouse.fictitious_id"
                            />
                        </el-select>
                    </div>

                    <!-- 查询按钮 -->
                    <div class="filter-item">
                        <el-button type="primary" @click="searchTurnoverData" :loading="loading">
                            查询
                        </el-button>
                        <el-button @click="resetFilters">重置</el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div v-if="showTable && turnoverData.length > 0" class="table-section">
            <div class="table-header">
                <h3>商品数据详情</h3>
                <div class="table-actions">
                    <el-button type="success" @click="exportData" :loading="exportLoading">
                        <i class="fas fa-download"></i> 导出数据
                    </el-button>
                </div>
            </div>

            <el-table 
                :data="turnoverData" 
                v-loading="loading"
                stripe
                border
                style="width: 100%"
            >
                <el-table-column prop="bar_code" label="条码" width="150" />
                <el-table-column prop="short_code" label="简码" width="120" />
                <el-table-column prop="goods_name" label="中文品名" min-width="200" />
                <el-table-column prop="fictitious_name" label="虚拟仓名称" width="150" />
                <el-table-column prop="in_count" label="入库数量" width="100" align="right" />
                <el-table-column prop="out_count" label="出库数量" width="100" align="right" />
                <el-table-column prop="qc_nums" label="期初数量" width="100" align="right" />
                <el-table-column 
                    prop="turnover_rate" 
                    label="周转率" 
                    width="120" 
                    align="right"
                    sortable
                    :sort-method="sortTurnoverRate"
                >
                    <template slot-scope="scope">
                        <span :class="getTurnoverRateClass(scope.row.turnover_rate)">
                            {{ scope.row.turnover_rate }}%
                        </span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pagination.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                />
            </div>
        </div>

        <!-- 空状态 -->
        <div v-if="showTable && turnoverData.length === 0 && !loading" class="empty-state">
            <i class="fas fa-inbox"></i>
            <p>暂无数据</p>
        </div>
    </div>
</template>

<script>
import inventoryDashboardService from "@/services/inventory-dashboard";
import fileDownload from "js-file-download";

export default {
    name: "InventoryDashboard",
    data() {
        return {
            // 统计数据
            dashboardData: {
                today_in: 0,
                today_in_percent: 0,
                today_out: 0,
                today_out_percent: 0,
                month_in: 0,
                month_out: 0,
                year_in: 0,
                year_out: 0,
            },
            
            // 联系人和虚拟仓数据
            contactList: [],
            warehouseList: [],
            
            // 筛选器
            filters: {
                selectedName: "",
                skuSearch: "",
                startDate: this.getDefaultStartDate(),
                endDate: this.getDefaultEndDate(),
                selectedWarehouse: null,
            },
            
            // 显示控制
            showAdvancedFilters: false,
            showTable: false,
            
            // 周转率数据
            turnoverData: [],
            
            // 分页
            pagination: {
                page: 1,
                limit: 20,
                total: 0,
            },
            
            // 加载状态
            loading: false,
            exportLoading: false,
        };
    },
    
    mounted() {
        this.initData();
    },
    
    methods: {
        // 获取默认开始日期（当前年份的1月）
        getDefaultStartDate() {
            const now = new Date();
            return `${now.getFullYear()}-01`;
        },

        // 获取默认结束日期（当前月份）
        getDefaultEndDate() {
            const now = new Date();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            return `${now.getFullYear()}-${month}`;
        },

        // 初始化数据
        async initData() {
            await this.loadDashboardData();
            await this.loadContactWarehouse();
        },
        
        // 加载统计数据
        async loadDashboardData() {
            try {
                const response = await inventoryDashboardService.getInventoryBoard();
                if (response.data && response.data.data) {
                    this.dashboardData = response.data.data;
                }
            } catch (error) {
                console.error("加载统计数据失败:", error);
                this.$message.error("加载统计数据失败");
            }
        },
        
        // 加载联系人和虚拟仓数据
        async loadContactWarehouse() {
            try {
                const response = await inventoryDashboardService.getContactWarehouse();
                if (response.data && response.data.data && response.data.data.list) {
                    this.contactList = response.data.data.list;
                    // 提取所有虚拟仓数据
                    this.warehouseList = [];
                    this.contactList.forEach(contact => {
                        if (contact.fictitious_list) {
                            this.warehouseList.push(...contact.fictitious_list);
                        }
                    });
                }
            } catch (error) {
                console.error("加载联系人数据失败:", error);
                this.$message.error("加载联系人数据失败");
            }
        },
        
        // 姓名选择变化
        onNameChange(value) {
            if (value) {
                this.filters.skuSearch = "";
                this.updateWarehouseList();
            }
            this.showTable = false;
        },
        
        // SKU输入变化
        onSkuInput(value) {
            if (value) {
                this.filters.selectedName = "";
            }
            this.showTable = false;
        },
        
        // 更新虚拟仓列表（根据选择的姓名）
        updateWarehouseList() {
            if (this.filters.selectedName) {
                const selectedContact = this.contactList.find(
                    contact => contact.contacts_name === this.filters.selectedName
                );
                if (selectedContact && selectedContact.fictitious_list) {
                    this.warehouseList = selectedContact.fictitious_list;
                } else {
                    this.warehouseList = [];
                }
            } else {
                // 显示所有虚拟仓
                this.warehouseList = [];
                this.contactList.forEach(contact => {
                    if (contact.fictitious_list) {
                        this.warehouseList.push(...contact.fictitious_list);
                    }
                });
            }
            this.filters.selectedWarehouse = null;
        },
        
        // 搜索周转率数据
        async searchTurnoverData() {
            if (!this.filters.selectedName && !this.filters.skuSearch) {
                this.$message.warning("请选择姓名或输入SKU简码");
                return;
            }
            
            if (!this.filters.startDate || !this.filters.endDate) {
                this.$message.warning("请选择开始时间和结束时间");
                return;
            }
            
            this.loading = true;
            try {
                const params = {
                    page: this.pagination.page,
                    limit: this.pagination.limit,
                    contacts_name: this.filters.selectedName,
                    start_date: this.filters.startDate,
                    end_date: this.filters.endDate,
                };
                
                if (this.filters.skuSearch) {
                    params.short_code = this.filters.skuSearch;
                }
                
                if (this.filters.selectedWarehouse) {
                    params.fictitious_id = this.filters.selectedWarehouse;
                }
                
                const response = await inventoryDashboardService.getInventoryTurnover(params);
                if (response.data && response.data.data) {
                    this.turnoverData = response.data.data.list || [];
                    this.pagination.total = response.data.data.total || 0;
                    this.showTable = true;
                }
            } catch (error) {
                console.error("查询周转率数据失败:", error);
                this.$message.error("查询周转率数据失败");
            } finally {
                this.loading = false;
            }
        },
        
        // 重置筛选器
        resetFilters() {
            this.filters = {
                selectedName: "",
                skuSearch: "",
                startDate: this.getDefaultStartDate(),
                endDate: this.getDefaultEndDate(),
                selectedWarehouse: null,
            };
            this.showAdvancedFilters = false;
            this.showTable = false;
            this.turnoverData = [];
            this.pagination.page = 1;
            this.updateWarehouseList();
        },
        
        // 导出数据
        async exportData() {
            if (!this.filters.selectedName && !this.filters.skuSearch) {
                this.$message.warning("请先查询数据");
                return;
            }
            
            this.exportLoading = true;
            try {
                const params = {
                    contacts_name: this.filters.selectedName,
                    start_date: this.filters.startDate,
                    end_date: this.filters.endDate,
                };
                
                if (this.filters.skuSearch) {
                    params.short_code = this.filters.skuSearch;
                }
                
                if (this.filters.selectedWarehouse) {
                    params.fictitious_id = this.filters.selectedWarehouse;
                }
                
                const response = await inventoryDashboardService.exportInventoryTurnover(params);
                const fileName = `库存周转率数据_${new Date().toISOString().slice(0, 10)}.xlsx`;
                fileDownload(response.data, fileName);
                this.$message.success("导出成功");
            } catch (error) {
                console.error("导出失败:", error);
                this.$message.error("导出失败");
            } finally {
                this.exportLoading = false;
            }
        },
        
        // 分页大小变化
        handleSizeChange(val) {
            this.pagination.limit = val;
            this.pagination.page = 1;
            this.searchTurnoverData();
        },
        
        // 当前页变化
        handleCurrentChange(val) {
            this.pagination.page = val;
            this.searchTurnoverData();
        },
        
        // 周转率排序
        sortTurnoverRate(a, b) {
            return a.turnover_rate - b.turnover_rate;
        },
        
        // 获取周转率样式类
        getTurnoverRateClass(rate) {
            if (rate >= 90) return "high-rate";
            if (rate >= 70) return "medium-rate";
            return "low-rate";
        },
    },
};
</script>

<style scoped>
.inventory-dashboard {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
}

.page-header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 统计卡片样式 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f9ff;
}

.stats-icon i {
    font-size: 24px;
}

.stats-content h3 {
    font-size: 14px;
    color: #666;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.stats-number {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.stats-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-change.positive {
    color: #10b981;
}

.stats-change.negative {
    color: #ef4444;
}

/* 筛选区域样式 */
.filter-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section h2 {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
}

.filter-description {
    color: #666;
    font-size: 14px;
    margin: 0 0 20px 0;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-item label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.advanced-filters {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

/* 表格区域样式 */
.table-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3 {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 12px;
}

/* 周转率颜色样式 */
.high-rate {
    color: #10b981;
    font-weight: bold;
}

.medium-rate {
    color: #f59e0b;
    font-weight: bold;
}

.low-rate {
    color: #ef4444;
    font-weight: bold;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
}

.empty-state p {
    font-size: 16px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .inventory-dashboard {
        padding: 16px;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .table-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
}
</style>
