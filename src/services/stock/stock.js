import axios from "axios";

function getStockDetailPhysical(data) {
    // 物理仓库列表
    return axios({
        url: "/api/warehouse/list",
        method: "get",
        params: data,
    });
}
function updateStockDetailPhysical(data) {
    // 更新物理仓库详情
    return axios({
        url: "/api/warehouse/update",
        method: "post",
        data,
    });
}
function getStockAreaList(data) {
    // 获取分区列表
    return axios({
        url: "/admin/stock/area/list",
        method: "get",
        params: data,
    });
}
function updateStockAreaList(data) {
    // 更新分区
    return axios({
        url: "/admin/stock/area/update",
        method: "post",
        data,
    });
}
function createStockAreaList(data) {
    // 添加分区
    return axios({
        url: "/admin/stock/area/add",
        method: "post",
        data,
    });
}
function createdTransfer(data) {
    // 添加任务
    return axios({
        url: "/admin/outbound/transfer/created",
        method: "post",
        data,
    });
}

function getStockStorageList(data) {
    // 获取库位列表
    return axios({
        url: "/admin/stock/location/list",
        method: "get",
        params: data,
    });
}
function updateStockStorageList(data) {
    // 更新库位
    return axios({
        url: "/admin/stock/location/update",
        method: "post",
        data,
    });
}
function createStockStorageList(data) {
    // 添加库位
    return axios({
        url: "/admin/stock/location/add",
        method: "post",
        data,
    });
}
function getStockStorageType() {
    // 获取库位属性
    return axios({
        url: "/admin/stock/location/getAttrList",
        method: "get",
    });
}

function getStockShelfList(data) {
    // 获取货架列表
    return axios({
        url: "/admin/stock/rack/list",
        method: "get",
        params: data,
    });
}
function updateStockShelfList(data) {
    // 更新货架
    return axios({
        url: "/admin/stock/rack/update",
        method: "post",
        data,
    });
}
function createStockShelfList(data) {
    // 添加货架
    return axios({
        url: "/admin/stock/rack/add",
        method: "post",
        data,
    });
}
function getStockPlaceList(data) {
    // 获取货位列表
    return axios({
        url: "/admin/stock/seat/list",
        method: "get",
        params: data,
    });
}
function updateStockPlaceList(data) {
    // 更新货位
    return axios({
        url: "/admin/stock/seat/update",
        method: "post",
        data,
    });
}
function createStockPlaceList(data) {
    // 添加货位
    return axios({
        url: "/admin/stock/seat/add",
        method: "post",
        data,
    });
}
function getStockLinkage(type) {
    // 获取仓库整个模块的联动菜单
    return axios({
        url: "/admin/stock/getStockLinkage?type=" + type,
        method: "get",
    });
}
function getVirtualList(data) {
    // 获取虚拟仓库列表
    return axios({
        url: "/admin/stock/fictitious/list",
        method: "get",
        params: data,
    });
}
function updateVirtual(data) {
    // 更新虚拟仓库
    return axios({
        url: "/admin/stock/fictitious/update",
        method: "post",
        data,
    });
}
function addVirtual(data) {
    // 新增虚拟仓库
    return axios({
        url: "/admin/stock/fictitious/add",
        method: "post",
        data,
    });
}
function getStorageGoodsVirtual(data) {
    // 获取虚拟仓库商品列表
    return axios({
        url: "/admin/stock/fictitiousGoods/list",
        method: "get",
        params: data,
    });
}
function updateWarning(data) {
    // 批量更新预警值
    return axios({
        url: "/admin/stock/location/updateWarning",
        method: "post",
        data,
    });
}
function createWare(data) {
    // 创建物理仓库
    return axios({
        url: "/api/warehouse/create",
        method: "post",
        data,
    });
}
function shiftAciton(data) {
    // 虚拟仓库移库
    return axios({
        url: "/admin/stock/fictitious/allocation",
        method: "post",
        data,
    });
}
function reduceCount(data) {
    // 虚拟仓库减少库存
    return axios({
        url: "/admin/stock/fictitious/reduceCount",
        method: "post",
        data,
    });
}

function getVersion() {
    // 获取版本
    return axios({
        url: "/api/apk/getVersion",
        method: "get",
    });
}
function exportWorkOverview(data) {
    // 导出工作报告
    return axios({
        url: "/admin/outbound/statistics/exportWorkOverview",
        method: "get",
        params: data,
        responseType: "blob",
    });
}
function getWayManageList() {
    // 模板列表
    return axios({
        url: "/admin/outbound/experss_template/list",
        method: "get",
    });
}
//获取指定地区
function getAreaList(data) {
    return axios({
        url: "/admin/outbound/experss_template/area",
        method: "get",
        params: data,
    });
}
//获取业务列表
function getbusinessListt(data) {
    return axios({
        url: "/admin/outbound/experss_template/business_list",
        method: "get",
        params: data,
    });
}

function addExperssTemplate(data) {
    // 添加模板
    return axios({
        url: "/admin/outbound/experss_template/add",
        method: "post",
        data,
    });
}
function editExperssTemplate(data) {
    // 编辑模板
    return axios({
        url: "/admin/outbound/experss_template/edit",
        method: "post",
        data,
    });
}

function changeStatus(data) {
    // 编辑模板
    return axios({
        url: "/admin/outbound/experss_template/updateStatus",
        method: "post",
        data,
    });
}
function ExpressShipBoard(data) {
    // 快递发货看板
    return axios({
        url: "/admin/outbound/statistics/ExpressShipBoard",
        method: "get",
        params: data,
    });
}

export default {
    reduceCount,
    updateStockDetailPhysical,
    createWare,
    ExpressShipBoard,
    exportWorkOverview,
    updateWarning,
    getVersion,
    getStockDetailPhysical,
    shiftAciton,
    addVirtual,
    createStockPlaceList,
    updateVirtual,
    getStorageGoodsVirtual,
    getVirtualList,
    getStockPlaceList,
    updateStockPlaceList,
    updateStockShelfList,
    createStockAreaList,
    createStockShelfList,
    updateStockAreaList,
    getStockShelfList,
    getStockAreaList,
    getStockStorageList,
    getStockStorageType,
    createdTransfer,
    getStockLinkage,
    createStockStorageList,
    updateStockStorageList,
    getWayManageList,
    getAreaList,
    getbusinessListt,
    addExperssTemplate,
    editExperssTemplate,
    changeStatus,
};
