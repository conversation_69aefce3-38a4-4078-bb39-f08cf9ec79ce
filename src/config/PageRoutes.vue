<script>
import VueRouter from "vue-router";
import Login from "../pages/user/login";
import Home from "../pages/Home";
import permissionsList from "../pages/permissions/permissionsList";
import physicalDetail from "../pages/warehouse/physical/detail";
import AddPermissions from "../pages/permissions/addPermission";
import area from "../pages/warehouse/physical/area";
import storage from "../pages/warehouse/physical/storage";
import shelf from "../pages/warehouse/physical/shelf";
import place from "../pages/warehouse/physical/place";
import goods from "../pages/goods/list";
import TransferGDList from "@/pages/transferGD/list";
import storageGoodsPhysical from "../pages/storageGoods/storageGoodsPhysical";
import warehousing from "../pages/order/warehousing/warehousing";

import clearTask from "../pages/order/warehousing/clearTask";
import upTask from "../pages/order/warehousing/upTask";
import ordertask from "../pages/order/createWave/ordertask";
import packaging from "../pages/packaging/packaging";
import virtual from "../pages/warehouse/virtual/index";
import outboundOrder from "../pages/order/outboundOrder/index";
import packTool from "../pages/packaging/packTool";
import allot from "../pages/order/warehousing/allot";
import logisticsPackaging from "../pages/packaging/logisticsPackaging";
import userManagement from "../pages/permissions/userManagement";
import roleManagement from "../pages/permissions/roleManagement";
import NotFound from "../pages/notFound";
import refundOrder from "../pages/order/refund/index";
import dashboard from "../pages/dashboard/index";
import allotOrder from "../pages/order/warehousing/allotOrder.vue";
import warehousingOrder from "../pages/order/warehousing/warehousingOrder.vue";
import cancelOrderList from "../pages/order/cancel/index";
import PurchaseOrder from "@/pages/order/purchaseOrder";
import CheckStockPlanList from "@/pages/checkStock/planList";
import CheckStockTaskList from "@/pages/checkStock/taskList";
import CheckStockDiffList from "@/pages/checkStock/diffList";
import customOrder from "../pages/order/customOrder/customOrder";
import CourierWayManage from "../pages/warehouse/CourierWayManage/index";
import expressBoard from "../pages/dashboard/expressBoard.vue";
import RRSManagement from "../pages/RRSManagement/index.vue";
import ReturnInventory from "../pages/returnInventory/index.vue";
const routes = [
    { path: "/", component: Login, meta: "login" },
    { path: "/dashboard", component: dashboard, meta: "dashboard" },
    { path: "/expressBoard", component: expressBoard, meta: "expressBoard" },

    { path: "/home", component: Home, meta: "home" },
    {
        // 权限列表
        path: "/permissionsList",
        component: permissionsList,
        meta: "permissionsList",
    },
    {
        //添加权限
        path: "/addPermission",
        component: AddPermissions,
        meta: "addPermission",
    },
    {
        //物理仓库详情
        path: "/physical-detail",
        component: physicalDetail,
        meta: "physical-detail",
    },
    {
        //物理仓库-分区
        path: "/area",
        component: area,
        meta: "area",
    },
    {
        //物理仓库-库位
        path: "/storage",
        component: storage,
        meta: "storage",
    },
    {
        //物理仓库-货架
        path: "/shelf",
        component: shelf,
        meta: "shelf",
    },
    {
        //物理仓库-货位
        path: "/place",
        component: place,
        meta: "place",
    },
    {
        //商品档案-列表
        path: "/goods",
        component: goods,
        meta: "goods",
    },
    {
        path: "/transferGDList",
        component: TransferGDList,
        meta: "/transferGDList",
    },

    {
        //入库单列表
        path: "/warehousingOrder",
        component: warehousingOrder,
        meta: "warehousingOrder",
    },
    {
        //入库单列表
        path: "/warehousing",
        component: warehousing,
        meta: "warehousing",
    },
    {
        //物理仓库-商品列表
        path: "/storageGoodsPhysical",
        component: storageGoodsPhysical,
        meta: "storageGoodsPhysical",
    },

    {
        //入库单-上架任务列表
        path: "/upTask",
        component: upTask,
        meta: "upTask",
    },
    {
        //入库单-清点任务列表
        path: "/clearTask",
        component: clearTask,
        meta: "clearTask",
    },
    {
        //订单-生成波次
        path: "/ordertask",
        component: ordertask,
        meta: "ordertask",
    },
    {
        // 打包复核
        path: "/packaging",
        component: packaging,
        meta: "packaging",
    },
    {
        // 虚拟仓
        path: "/virtual",
        component: virtual,
        meta: "virtual",
    },
    {
        // 出库单
        path: "/outboundOrder",
        component: outboundOrder,
        meta: "/outboundOrder",
    },
    {
        // 打包复核 增加包裹、重打面单
        path: "/packTool",
        component: packTool,
        meta: "/packTool",
    },
    {
        // 新·调拨入库单管理
        path: "/allotOrder",
        component: allotOrder,
        meta: "/allotOrder",
    },
    {
        // 调拨入库单管理
        path: "/allot",
        component: allot,
        meta: "/allot",
    },
    {
        // 调拨入库单管理
        path: "/customOrder",
        component: customOrder,
        meta: "/customOrder",
    },
    {
        // 物流打包
        path: "/logisticsPackaging",
        component: logisticsPackaging,
        meta: "/logisticsPackaging",
    },
    {
        // 用户管理
        path: "/userManagement",
        component: userManagement,
        meta: "/userManagement",
    },
    {
        // 角色管理
        path: "/roleManagement",
        component: roleManagement,
        meta: "/roleManagement",
    },
    {
        // 退货单
        path: "/refundOrder",
        component: refundOrder,
        meta: "/refundOrder",
    },
    // {
    //     // 退货单商品列表
    //     path: "/refundGoodsAdd",
    //     component: refundOrderAdd,
    //     meta: "/refundGoodsAdd"
    // },

    {
        // 撤单上架任务
        path: "/cancelOrderList",
        component: cancelOrderList,
        meta: "/cancelOrderList",
    },
    {
        path: "/purchaseOrder",
        component: PurchaseOrder,
        meta: "/purchaseOrder",
    },
    {
        path: "/checkStockPlanList",
        component: CheckStockPlanList,
        meta: "/checkStockPlanList",
    },
    {
        path: "/checkStockTaskList",
        component: CheckStockTaskList,
        meta: "/checkStockTaskList",
    },
    {
        path: "/checkStockDiffList",
        component: CheckStockDiffList,
        meta: "/checkStockDiffList",
    },
    {
        path: "/courierWayManage",
        component: CourierWayManage,
        meta: "/courierWayManage",
    },
    {
        path: "/RRSManagement",
        component: RRSManagement,
        meta: "/RRSManagement",
    },
    {
        path: "/returnInventory",
        component: ReturnInventory,
        meta: "/returnInventory",
    },
    
    { path: "*", component: NotFound },
];

const router = new VueRouter({
    routes,
});

export default router;
</script>
