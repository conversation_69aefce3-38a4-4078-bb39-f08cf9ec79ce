"use client"

import { useState } from "react"
import { TrendingUp, TrendingDown, Package, PackageOpen } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function Component() {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [selectedName, setSelectedName] = useState("")
  const [startYear, setStartYear] = useState("2024")
  const [startMonth, setStartMonth] = useState("1")
  const [endYear, setEndYear] = useState("2024")
  const [endMonth, setEndMonth] = useState("12")
  const [selectedWarehouse, setSelectedWarehouse] = useState("all")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | null>(null)
  const [skuSearch, setSkuSearch] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [showTable, setShowTable] = useState(false)

  // 模拟数据
  const dailyData = {
    inbound: 1250,
    outbound: 980,
    inboundYoY: 12.5,
    outboundYoY: -3.2,
  }

  const monthlyData = {
    inbound: 35600,
    outbound: 32800,
  }

  const yearlyData = {
    inbound: 425000,
    outbound: 398000,
  }

  const dailyTurnoverRate = ((dailyData.outbound / dailyData.inbound) * 100).toFixed(1)

  const productData = [
    { sku: "SKU001", name: "苹果手机", turnoverRate: 95.2, warehouse: "虚拟仓A" },
    { sku: "SKU002", name: "华为平板", turnoverRate: 87.6, warehouse: "虚拟仓B" },
    { sku: "SKU003", name: "小米耳机", turnoverRate: 92.1, warehouse: "虚拟仓A" },
    { sku: "SKU004", name: "联想笔记本", turnoverRate: 78.9, warehouse: "虚拟仓C" },
    { sku: "SKU005", name: "戴尔显示器", turnoverRate: 85.3, warehouse: "虚拟仓B" },
    { sku: "SKU006", name: "罗技鼠标", turnoverRate: 91.7, warehouse: "虚拟仓D" },
    { sku: "SKU007", name: "机械键盘", turnoverRate: 89.4, warehouse: "虚拟仓A" },
    { sku: "SKU008", name: "无线充电器", turnoverRate: 93.8, warehouse: "虚拟仓C" },
  ]

  const filteredProductData = productData.filter((product) => {
    const warehouseMatch = selectedWarehouse === "all" || product.warehouse === getWarehouseName(selectedWarehouse)
    const skuMatch = skuSearch.trim()
      ? product.sku.toLowerCase().includes(skuSearch.toLowerCase()) ||
        product.name.toLowerCase().includes(skuSearch.toLowerCase())
      : true
    return warehouseMatch && skuMatch
  })

  const sortedProductData = [...filteredProductData].sort((a, b) => {
    if (sortOrder === "asc") {
      return a.turnoverRate - b.turnoverRate
    } else if (sortOrder === "desc") {
      return b.turnoverRate - a.turnoverRate
    }
    return 0
  })

  const totalItems = sortedProductData.length
  const totalPages = Math.ceil(totalItems / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedData = sortedProductData.slice(startIndex, endIndex)

  function getWarehouseName(value: string) {
    const warehouseMap: { [key: string]: string } = {
      "warehouse-a": "虚拟仓A",
      "warehouse-b": "虚拟仓B",
      "warehouse-c": "虚拟仓C",
      "warehouse-d": "虚拟仓D",
    }
    return warehouseMap[value] || ""
  }

  return (
    <div className="min-h-screen bg-gray-50/50 p-4 md:p-6">
      <div className="mx-auto max-w-7xl space-y-6">
        {/* 页面标题和时间选择器 */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight md:text-3xl">库存数据看板</h1>
            <p className="text-muted-foreground">实时监控库存周转率和进出库数据</p>
          </div>
        </div>

        {/* 数量指标卡片 - 2行3列布局 */}
        <div className="grid gap-4 grid-cols-1 md:grid-cols-3 grid-rows-2">
          {/* 第一行 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">当日入库数量</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dailyData.inbound.toLocaleString()}</div>
              <div className="flex items-center text-xs">
                {dailyData.inboundYoY > 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={dailyData.inboundYoY > 0 ? "text-green-500" : "text-red-500"}>
                  {dailyData.inboundYoY > 0 ? "+" : ""}
                  {dailyData.inboundYoY}%
                </span>
                <span className="ml-1 text-muted-foreground">同比去年</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">当日出库数量</CardTitle>
              <PackageOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dailyData.outbound.toLocaleString()}</div>
              <div className="flex items-center text-xs">
                {dailyData.outboundYoY > 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={dailyData.outboundYoY > 0 ? "text-green-500" : "text-red-500"}>
                  {dailyData.outboundYoY > 0 ? "+" : ""}
                  {dailyData.outboundYoY}%
                </span>
                <span className="ml-1 text-muted-foreground">同比去年</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">本月入库数量</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{monthlyData.inbound.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">本月累计</p>
            </CardContent>
          </Card>

          {/* 第二行 */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">本月出库数量</CardTitle>
              <PackageOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{monthlyData.outbound.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">本月累计</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">年度入库数量</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyData.inbound.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">{startYear}年累计</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">年度出库数量</CardTitle>
              <PackageOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyData.outbound.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">{startYear}年累计</p>
            </CardContent>
          </Card>
        </div>

        {/* 筛选器区域 */}
        <Card>
          <CardHeader>
            <CardTitle>库存周转率</CardTitle>
            <CardDescription>请选择姓名或输入SKU进行查询</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 姓名选择 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">姓名</label>
                <Select
                  value={selectedName}
                  onValueChange={(value) => {
                    setSelectedName(value)
                    setSkuSearch("")
                    setShowFilters(true)
                    setShowTable(false)
                    setCurrentPage(1)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择姓名" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="张三">张三</SelectItem>
                    <SelectItem value="李四">李四</SelectItem>
                    <SelectItem value="王五">王五</SelectItem>
                    <SelectItem value="赵六">赵六</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* SKU搜索 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">SKU搜索</label>
                <input
                  type="text"
                  value={skuSearch}
                  onChange={(e) => {
                    setSkuSearch(e.target.value)
                    setSelectedName("")
                    setShowTable(false)
                    setCurrentPage(1)
                  }}
                  onFocus={() => {
                    setShowFilters(true)
                  }}
                  placeholder="请输入SKU"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              {/* 开始时间 */}
              {showFilters && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">开始时间</label>
                  <div className="flex gap-2">
                    <Select
                      value={startYear}
                      onValueChange={(value) => {
                        setStartYear(value)
                        setShowTable(false)
                        setCurrentPage(1)
                      }}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2024">2024年</SelectItem>
                        <SelectItem value="2023">2023年</SelectItem>
                        <SelectItem value="2022">2022年</SelectItem>
                        <SelectItem value="2021">2021年</SelectItem>
                        <SelectItem value="2020">2020年</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select
                      value={startMonth}
                      onValueChange={(value) => {
                        setStartMonth(value)
                        setShowTable(false)
                        setCurrentPage(1)
                      }}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => (
                          <SelectItem key={i + 1} value={String(i + 1)}>
                            {i + 1}月
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {/* 结束时间 */}
              {showFilters && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">结束时间</label>
                  <div className="flex gap-2">
                    <Select
                      value={endYear}
                      onValueChange={(value) => {
                        setEndYear(value)
                        setShowTable(false)
                        setCurrentPage(1)
                      }}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2024">2024年</SelectItem>
                        <SelectItem value="2023">2023年</SelectItem>
                        <SelectItem value="2022">2022年</SelectItem>
                        <SelectItem value="2021">2021年</SelectItem>
                        <SelectItem value="2020">2020年</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select
                      value={endMonth}
                      onValueChange={(value) => {
                        setEndMonth(value)
                        setShowTable(false)
                        setCurrentPage(1)
                      }}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 12 }, (_, i) => (
                          <SelectItem key={i + 1} value={String(i + 1)}>
                            {i + 1}月
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>

            {/* 虚拟仓选择和查询按钮 */}
            {showFilters && (
              <div className="mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">虚拟仓</label>
                  <Select
                    value={selectedWarehouse}
                    onValueChange={(value) => {
                      setSelectedWarehouse(value)
                      setShowTable(false)
                      setCurrentPage(1)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部虚拟仓</SelectItem>
                      <SelectItem value="warehouse-a">虚拟仓A</SelectItem>
                      <SelectItem value="warehouse-b">虚拟仓B</SelectItem>
                      <SelectItem value="warehouse-c">虚拟仓C</SelectItem>
                      <SelectItem value="warehouse-d">虚拟仓D</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">&nbsp;</label>
                  <button
                    onClick={() => {
                      if (selectedName || skuSearch.trim()) {
                        setShowTable(true)
                        setCurrentPage(1)
                      }
                    }}
                    disabled={!selectedName && !skuSearch.trim()}
                    className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 w-full"
                  >
                    查询
                  </button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {showTable && (selectedName || skuSearch.trim()) && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>商品数据详情</CardTitle>
                  <CardDescription>
                    {selectedName
                      ? `${selectedName} - ${startYear}年${startMonth}月 至 ${endYear}年${endMonth}月`
                      : `SKU: ${skuSearch} - ${startYear}年${startMonth}月 至 ${endYear}年${endMonth}月`}
                    {selectedWarehouse !== "all" && ` - ${getWarehouseName(selectedWarehouse)}`}
                  </CardDescription>
                </div>
                <button
                  onClick={() => {
                    // 创建CSV内容
                    const headers = ["SKU", "商品名称", "周转率", "虚拟仓"]
                    const csvContent = [
                      headers.join(","),
                      ...sortedProductData.map((product) =>
                        [product.sku, product.name, `${product.turnoverRate}%`, product.warehouse].join(","),
                      ),
                    ].join("\n")

                    // 创建并下载文件
                    const blob = new Blob(["\ufeff" + csvContent], { type: "text/csv;charset=utf-8;" })
                    const link = document.createElement("a")
                    const url = URL.createObjectURL(blob)
                    link.setAttribute("href", url)
                    link.setAttribute(
                      "download",
                      `商品数据详情_${selectedName}_${startYear}${startMonth}-${endYear}${endMonth}.csv`,
                    )
                    link.style.visibility = "hidden"
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link)
                  }}
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
                >
                  导出
                </button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 font-medium">SKU</th>
                      <th className="text-left p-3 font-medium">商品名称</th>
                      <th
                        className="text-left p-3 font-medium cursor-pointer hover:bg-gray-100 select-none"
                        onClick={() => {
                          if (sortOrder === null || sortOrder === "desc") {
                            setSortOrder("asc")
                          } else {
                            setSortOrder("desc")
                          }
                        }}
                      >
                        <div className="flex items-center gap-1">
                          周转率
                          {sortOrder === "asc" && <span className="text-blue-500">↑</span>}
                          {sortOrder === "desc" && <span className="text-blue-500">↓</span>}
                          {sortOrder === null && <span className="text-gray-400">↕</span>}
                        </div>
                      </th>
                      <th className="text-left p-3 font-medium">虚拟仓</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.map((product, index) => (
                      <tr key={product.sku} className={index % 2 === 0 ? "bg-gray-50/50" : ""}>
                        <td className="p-3 font-mono text-sm">{product.sku}</td>
                        <td className="p-3">{product.name}</td>
                        <td className="p-3">
                          <span
                            className={`font-medium ${
                              product.turnoverRate >= 90
                                ? "text-green-600"
                                : product.turnoverRate >= 80
                                  ? "text-yellow-600"
                                  : "text-red-600"
                            }`}
                          >
                            {product.turnoverRate}%
                          </span>
                        </td>
                        <td className="p-3">
                          <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                            {product.warehouse}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {filteredProductData.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">暂无数据</div>
              )}
            </CardContent>
          </Card>
        )}
        {totalItems > 0 && (
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                显示 {startIndex + 1} - {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条
              </span>
              <Select
                value={String(pageSize)}
                onValueChange={(value) => {
                  setPageSize(Number(value))
                  setCurrentPage(1)
                }}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-muted-foreground">条/页</span>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
              >
                上一页
              </button>
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 h-9 w-9 ${
                        currentPage === pageNum
                          ? "bg-primary text-primary-foreground"
                          : "border border-input bg-background hover:bg-accent hover:text-accent-foreground"
                      }`}
                    >
                      {pageNum}
                    </button>
                  )
                })}
              </div>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
